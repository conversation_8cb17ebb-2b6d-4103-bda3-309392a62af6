create table public.mixrag_documents
(
    doc_id              varchar(64)  not null
        primary key,
    original_filename   varchar(500) not null,
    file_extension      varchar(10),
    file_size           bigint,
    mime_type           varchar(100),
    minio_bucket        varchar(100) not null,
    minio_object_key    varchar(500) not null,
    content_hash        varchar(64),
    upload_status       varchar(20) default 'pending'::character varying,
    process_status      varchar(20) default 'pending'::character varying,
    created_at          timestamp   default CURRENT_TIMESTAMP,
    updated_at          timestamp   default CURRENT_TIMESTAMP,
    uploaded_by         varchar(100),
    error_message       text,
    chunks_count        integer     default 0,
    entities_count      integer     default 0,
    relationships_count integer     default 0,
    pipeline_id         varchar(50)
);

alter table public.mixrag_documents
    owner to root;

create index idx_documents_upload_status
    on public.mixrag_documents (upload_status);

create index idx_documents_process_status
    on public.mixrag_documents (process_status);

create index idx_documents_created_at
    on public.mixrag_documents (created_at);

create index idx_documents_minio_object_key
    on public.mixrag_documents (minio_object_key);

create index idx_documents_pipeline_id
    on public.mixrag_documents (pipeline_id);

create trigger update_documents_updated_at
    before update
    on public.mixrag_documents
    for each row
execute procedure public.update_updated_at_column();

create table public.mixrag_pipeline
(
    id            varchar(50)                                      not null
        primary key,
    name          varchar(200)                                     not null,
    description   text,
    status        varchar(20) default 'pending'::character varying not null,
    created_at    timestamp   default CURRENT_TIMESTAMP,
    started_at    timestamp,
    completed_at  timestamp,
    created_by    varchar(100),
    input_data    text,
    output_data   text,
    error_message text,
    doc_id        varchar(64)
);

alter table public.mixrag_pipeline
    owner to root;

create index idx_pipeline_status
    on public.mixrag_pipeline (status);

create table public.mixrag_pipeline_task
(
    id            varchar(50)                                      not null
        primary key,
    pipeline_id   varchar(50)                                      not null
        references public.mixrag_pipeline
            on delete cascade,
    task_type     varchar(50)                                      not null,
    status        varchar(20) default 'pending'::character varying not null,
    input_data    text,
    output_data   text,
    error_message text,
    retry_count   integer     default 0,
    max_retries   integer     default 3,
    step_order    integer     default 0                            not null,
    dependencies  text,
    created_at    timestamp   default CURRENT_TIMESTAMP,
    started_at    timestamp,
    completed_at  timestamp
);

alter table public.mixrag_pipeline_task
    owner to root;

create index idx_pipeline_tasks_pipeline_id
    on public.mixrag_pipeline_task (pipeline_id);

create index idx_pipeline_tasks_status_type
    on public.mixrag_pipeline_task (status, task_type);

create index idx_pipeline_tasks_step_order
    on public.mixrag_pipeline_task (pipeline_id, step_order);

